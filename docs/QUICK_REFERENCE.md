# Schema 模块快速参考

## 🚀 快速开始

```go
import "your-project/schema"

// 基本查询
sql, args, err := schema.GenerateSQLFromJSON(`{
    "select": ["id", "name"],
    "from": "users",
    "where": {"status": "active"},
    "limit": 10
}`)
```

## 📋 API 速查表

### 查询类
```go
// 基本查询
GenerateSQLFromJSON(jsonStr) (sql, args, error)

// 高级查询 (支持 >, <, IN, LIKE 等)
GenerateAdvancedSQL(jsonStr) (sql, args, error)

// 复杂嵌套查询 (支持 OR, AND 嵌套)
GenerateComplexSQL(jsonStr) (sql, args, error)

// 条件性查询 (IF-THEN-ELSE)
GenerateConditionalSQL(jsonStr) (sql, args, error)
```

### 数据操作类
```go
// 插入
GenerateInsertSQL(jsonStr) (sql, args, error)

// 批量插入
GenerateBatchInsertSQL(jsonStr) (sql, args, error)

// 更新
GenerateUpdateSQL(jsonStr) (sql, args, error)

// 删除
GenerateDeleteSQL(jsonStr) (sql, args, error)
```

### DDL 类
```go
// 创建表
GenerateCreateTableSQL(jsonStr) (sql, error)

// 创建索引
GenerateCreateIndexSQL(jsonStr) (sql, error)

// 修改表
GenerateAlterTableSQL(jsonStr) ([]sql, error)
```

## 🔧 操作符速查

| 操作符 | 用法 | 示例 |
|--------|------|------|
| `=` | 等于 | `{"op": "=", "v": "active"}` |
| `>` | 大于 | `{"op": ">", "v": 18}` |
| `<` | 小于 | `{"op": "<", "v": 65}` |
| `>=` | 大于等于 | `{"op": ">=", "v": 18}` |
| `<=` | 小于等于 | `{"op": "<=", "v": 65}` |
| `!=` | 不等于 | `{"op": "!=", "v": 0}` |
| `IN` | 包含 | `{"op": "IN", "v": ["a", "b"]}` |
| `NOT IN` | 不包含 | `{"op": "NOT IN", "v": ["x", "y"]}` |
| `LIKE` | 模糊匹配 | `{"op": "LIKE", "v": "%john%"}` |
| `NOT LIKE` | 不匹配 | `{"op": "NOT LIKE", "v": "%spam%"}` |
| `BETWEEN` | 范围 | `{"op": "BETWEEN", "v": [18, 65]}` |
| `IS NULL` | 为空 | `{"op": "IS NULL", "v": null}` |
| `IS NOT NULL` | 不为空 | `{"op": "IS NOT NULL", "v": null}` |

## 📝 常用模板

### 1. 基本查询模板
```json
{
    "select": ["字段1", "字段2"],
    "from": "表名",
    "where": {
        "字段": "值"
    },
    "order_by": ["字段 DESC"],
    "limit": 10,
    "offset": 0
}
```

### 2. JOIN 查询模板
```json
{
    "select": ["u.id", "p.title"],
    "from": "users u",
    "join_tables": [
        {
            "type": "LEFT",
            "table": "posts p",
            "condition": {"u.id": "p.user_id"}
        }
    ],
    "where": {"u.status": "active"}
}
```

### 3. 高级查询模板
```json
{
    "select": ["id", "name"],
    "from": "users",
    "adv_where": {
        "age": {"op": "BETWEEN", "v": [18, 65]},
        "status": {"op": "IN", "v": ["active", "pending"]},
        "name": {"op": "LIKE", "v": "%john%"}
    }
}
```

### 4. 复杂嵌套查询模板
```json
{
    "select": ["id", "name"],
    "from": "users",
    "complex_where": {
        "logic": "AND",
        "conditions": [
            {
                "field": "status",
                "operator": "=",
                "value": "active"
            },
            {
                "nested": {
                    "logic": "OR",
                    "conditions": [
                        {"field": "age", "operator": ">", "value": 18},
                        {"field": "vip", "operator": "=", "value": true}
                    ]
                }
            }
        ]
    }
}
```

### 5. 条件性查询模板
```json
{
    "select": ["id", "name"],
    "from": "users",
    "conditional_query": {
        "if": {
            "field": "vip_level",
            "operator": ">=",
            "value": 3
        },
        "then": {
            "where": {"discount": 0.15}
        },
        "else": {
            "where": {"discount": 0.05}
        }
    }
}
```

### 6. 插入模板
```json
{
    "table": "users",
    "values": {
        "name": "张三",
        "email": "<EMAIL>",
        "age": 25
    }
}
```

### 7. 批量插入模板
```json
{
    "table": "products",
    "columns": ["name", "price", "stock"],
    "values": [
        ["产品A", 99.99, 100],
        ["产品B", 199.99, 50],
        ["产品C", 299.99, 25]
    ]
}
```

### 8. 更新模板
```json
{
    "table": "users",
    "values": {
        "last_login": "2024-01-15 10:00:00",
        "status": "active"
    },
    "where": {
        "id": 123
    }
}
```

### 9. 删除模板
```json
{
    "table": "users",
    "where": {
        "status": "inactive",
        "last_login": "2023-01-01"
    }
}
```

### 10. 创建表模板
```json
{
    "name": "users",
    "columns": {
        "id": "bigint(20) NOT NULL AUTO_INCREMENT",
        "name": "varchar(255) NOT NULL",
        "email": "varchar(255) NOT NULL"
    },
    "constraints": {
        "PRIMARY": "PRIMARY KEY (id)",
        "uk_email": "UNIQUE KEY (email)"
    },
    "engine": "InnoDB",
    "charset": "utf8mb4",
    "comment": "用户表",
    "if_not_exists": true
}
```

### 11. 创建索引模板
```json
{
    "name": "idx_user_status",
    "table": "users",
    "columns": ["status", "created_at"],
    "unique": false,
    "if_not_exists": true
}
```

### 12. 修改表模板
```json
{
    "name": "users",
    "add_columns": {
        "phone": "varchar(20) DEFAULT NULL"
    },
    "drop_columns": ["old_field"],
    "modify_columns": {
        "name": "varchar(100) NOT NULL"
    },
    "rename_columns": {
        "old_email": "email"
    },
    "add_constraints": {
        "idx_phone": "INDEX (phone)"
    },
    "drop_constraints": ["old_index"]
}
```

## ⚠️ 常见错误

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `select字段不能为空` | select 数组为空 | 添加要查询的字段 |
| `from字段不能为空` | 缺少 from 字段 | 指定要查询的表名 |
| `不支持的操作符` | 使用了不存在的操作符 | 检查操作符拼写 |
| `BETWEEN操作符需要两个值的数组` | BETWEEN 值不是数组或长度不为2 | 使用 `[min, max]` 格式 |
| `不支持的逻辑操作符` | logic 不是 AND 或 OR | 使用 "AND" 或 "OR" |

## 💡 最佳实践

### ✅ 推荐做法
```go
// 1. 使用参数化查询（自动处理）
// 2. 限制查询结果数量
{"limit": 100}

// 3. 使用索引字段查询
{"where": {"email": "<EMAIL>"}}

// 4. 合理使用嵌套条件
{"complex_where": {"logic": "AND", "conditions": [...]}}
```

### ❌ 避免做法
```go
// 1. 避免查询大量数据不加限制
// 2. 避免在非索引字段上使用 LIKE
// 3. 避免过深的嵌套条件
```

## 🔗 相关文档

- [完整使用指南](./SCHEMA_MODULE_GUIDE.md)
- [复杂查询 JSON Schema](./complex_query_json_schema.md)
- [功能增强总结](./ENHANCEMENT_SUMMARY.md)
