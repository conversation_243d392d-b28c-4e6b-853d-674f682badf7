# Schema 模块文档总览

## 📚 文档导航

### 🚀 快速开始
- **[快速参考](./QUICK_REFERENCE.md)** - API 速查表和常用模板
- **[完整使用指南](./SCHEMA_MODULE_GUIDE.md)** - 详细的功能说明和用例

### 🔧 功能文档
- **[复杂查询 JSON Schema](./complex_query_json_schema.md)** - 复杂嵌套查询的详细定义
- **[功能增强总结](./ENHANCEMENT_SUMMARY.md)** - 新增功能的详细说明

## 🎯 模块概述

Schema 模块是一个强大的 SQL 构建器，基于 JSON 配置生成各种 SQL 语句。它提供了：

### 核心功能
- ✅ **查询构建** - 从简单查询到复杂嵌套条件
- ✅ **数据操作** - INSERT、UPDATE、DELETE、批量操作
- ✅ **DDL 支持** - 创建表、索引、修改表结构
- ✅ **安全性** - 自动参数化查询，防止 SQL 注入
- ✅ **类型安全** - 基于结构体的强类型定义

### 新增高级功能
- 🆕 **复杂嵌套查询** - 支持任意深度的 AND/OR 嵌套
- 🆕 **条件性查询** - 支持 IF-THEN-ELSE 逻辑
- 🆕 **扩展操作符** - BETWEEN、NOT IN、NOT LIKE、IS NOT NULL 等
- 🆕 **builder 模式** - 支持 builder.OR、builder.IF、builder.between 等

## 📋 API 概览

### 查询类函数
```go
// 基本查询
GenerateSQLFromJSON(jsonStr) (sql, args, error)

// 高级查询 (支持复杂操作符)
GenerateAdvancedSQL(jsonStr) (sql, args, error)

// 复杂嵌套查询 (支持 OR、AND 嵌套)
GenerateComplexSQL(jsonStr) (sql, args, error)

// 条件性查询 (IF-THEN-ELSE)
GenerateConditionalSQL(jsonStr) (sql, args, error)
```

### 数据操作函数
```go
// 数据操作
GenerateInsertSQL(jsonStr) (sql, args, error)
GenerateBatchInsertSQL(jsonStr) (sql, args, error)
GenerateUpdateSQL(jsonStr) (sql, args, error)
GenerateDeleteSQL(jsonStr) (sql, args, error)
```

### DDL 函数
```go
// DDL 操作
GenerateCreateTableSQL(jsonStr) (sql, error)
GenerateCreateIndexSQL(jsonStr) (sql, error)
GenerateAlterTableSQL(jsonStr) ([]sql, error)
```

## 🔥 特色功能示例

### 1. 复杂嵌套查询
```json
{
  "select": ["id", "name"],
  "from": "users",
  "complex_where": {
    "logic": "AND",
    "conditions": [
      {"field": "status", "operator": "=", "value": "active"},
      {
        "nested": {
          "logic": "OR",
          "conditions": [
            {"field": "age", "operator": ">", "value": 18},
            {"field": "vip_level", "operator": ">=", "value": 3}
          ]
        }
      }
    ]
  }
}
```

### 2. 条件性查询 (IF-THEN-ELSE)
```json
{
  "select": ["id", "name", "discount"],
  "from": "customers",
  "conditional_query": {
    "if": {"field": "vip_level", "operator": ">=", "value": 3},
    "then": {"where": {"discount": 0.15}},
    "else": {"where": {"discount": 0.05}}
  }
}
```

### 3. 高级操作符
```json
{
  "select": ["id", "name"],
  "from": "users",
  "adv_where": {
    "age": {"op": "BETWEEN", "v": [18, 65]},
    "status": {"op": "NOT IN", "v": ["banned", "deleted"]},
    "email": {"op": "IS NOT NULL", "v": null}
  }
}
```

## 🛠️ 安装和使用

### 依赖安装
```bash
go get xorm.io/builder
go get github.com/stretchr/testify/assert  # 用于测试
```

### 基本使用
```go
package main

import (
    "fmt"
    "log"
    "your-project/schema"
)

func main() {
    jsonStr := `{
        "select": ["id", "name"],
        "from": "users",
        "where": {"status": "active"},
        "limit": 10
    }`

    sql, args, err := schema.GenerateSQLFromJSON(jsonStr)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
}
```

## 📊 支持的操作符

| 类别 | 操作符 | 说明 | 示例 |
|------|--------|------|------|
| **基本** | `=`, `>`, `<`, `>=`, `<=`, `!=` | 基本比较 | `{"op": ">", "v": 18}` |
| **集合** | `IN`, `NOT IN` | 集合操作 | `{"op": "IN", "v": ["a", "b"]}` |
| **模糊** | `LIKE`, `NOT LIKE` | 模糊匹配 | `{"op": "LIKE", "v": "%john%"}` |
| **范围** | `BETWEEN` | 范围查询 | `{"op": "BETWEEN", "v": [18, 65]}` |
| **空值** | `IS NULL`, `IS NOT NULL` | 空值检查 | `{"op": "IS NULL", "v": null}` |

## 🧪 测试和验证

模块包含完整的测试套件：

```bash
# 运行所有测试
go test ./schema -v

# 运行复杂查询测试
go test ./schema/complex_query_test.go ./schema/xorm_builder.go -v

# 运行示例
go run examples/complex_query_examples.go
go run examples/documentation_examples.go
```

## 📁 文件结构

```
schema/
├── xorm_builder.go              # 主要功能实现
├── ddl_builder.go               # DDL 操作实现
├── complex_query_test.go        # 复杂查询测试
├── ddl_builder_test.go          # DDL 测试
├── xorm_builder_test.go         # 基本功能测试
└── README.md                    # 原始说明

docs/
├── README.md                    # 本文档
├── SCHEMA_MODULE_GUIDE.md       # 完整使用指南
├── QUICK_REFERENCE.md           # 快速参考
├── complex_query_json_schema.md # 复杂查询文档
└── ENHANCEMENT_SUMMARY.md      # 功能增强总结

examples/
├── complex_query_examples.go    # 复杂查询示例
└── documentation_examples.go    # 文档示例
```

## 🎯 适用场景

- **动态查询构建** - 根据用户输入动态生成查询
- **复杂报表查询** - 支持多层嵌套条件的报表
- **条件性业务逻辑** - 根据条件执行不同的查询逻辑
- **数据库迁移** - 通过 JSON 定义表结构和索引
- **API 查询参数转换** - 将 API 参数转换为 SQL 查询

## 🔗 相关链接

- [xorm/builder 官方文档](https://github.com/go-xorm/builder)
- [MySQL 官方文档](https://dev.mysql.com/doc/)

## 📞 支持

如有问题或建议，请查看：
1. [快速参考](./QUICK_REFERENCE.md) - 常见问题和解决方案
2. [完整指南](./SCHEMA_MODULE_GUIDE.md) - 详细的使用说明
3. 测试用例 - 查看具体的使用示例

---

**最后更新**: 2024-06-28  
**版本**: v2.0 (新增复杂嵌套查询功能)
