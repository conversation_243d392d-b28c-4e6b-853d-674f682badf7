# 复杂嵌套查询 JSON Schema 定义

本文档定义了支持复杂嵌套查询功能的JSON Schema结构，包括OR、IF、BETWEEN等高级查询功能。

## 1. 复杂条件查询 (ComplexCondition)

### 基本结构

```json
{
  "select": ["字段列表"],
  "from": "表名",
  "complex_where": {
    "logic": "AND|OR",
    "conditions": [
      {
        "field": "字段名",
        "operator": "操作符",
        "value": "值"
      }
    ]
  }
}
```

### 支持的操作符

| 操作符 | 说明 | 示例值 |
|--------|------|--------|
| `=` | 等于 | `"active"` |
| `>` | 大于 | `18` |
| `<` | 小于 | `65` |
| `>=` | 大于等于 | `18` |
| `<=` | 小于等于 | `65` |
| `!=`, `<>` | 不等于 | `0` |
| `IN` | 包含于 | `["active", "pending"]` |
| `NOT IN` | 不包含于 | `["banned", "deleted"]` |
| `LIKE` | 模糊匹配 | `"%john%"` |
| `NOT LIKE` | 不匹配 | `"%@spam.com"` |
| `IS NULL` | 为空 | `null` |
| `IS NOT NULL` | 不为空 | `null` |
| `BETWEEN` | 范围查询 | `[18, 65]` |

### 示例1: 基本OR条件

```json
{
  "select": ["id", "name", "age"],
  "from": "users",
  "complex_where": {
    "logic": "OR",
    "conditions": [
      {
        "field": "age",
        "operator": ">",
        "value": 18
      },
      {
        "field": "status",
        "operator": "=",
        "value": "vip"
      }
    ]
  }
}
```

生成SQL: `SELECT id,name,age FROM users WHERE (age>? OR status=?)`

### 示例2: 嵌套AND和OR条件

```json
{
  "select": ["id", "name"],
  "from": "users",
  "complex_where": {
    "logic": "AND",
    "conditions": [
      {
        "field": "status",
        "operator": "=",
        "value": "active"
      },
      {
        "nested": {
          "logic": "OR",
          "conditions": [
            {
              "field": "age",
              "operator": ">",
              "value": 18
            },
            {
              "field": "vip_level",
              "operator": ">=",
              "value": 3
            }
          ]
        }
      }
    ]
  }
}
```

生成SQL: `SELECT id,name FROM users WHERE (status=? AND (age>? OR vip_level>=?))`

### 示例3: BETWEEN操作符

```json
{
  "select": ["id", "name", "age"],
  "from": "users",
  "complex_where": {
    "logic": "AND",
    "conditions": [
      {
        "field": "age",
        "operator": "BETWEEN",
        "value": [18, 65]
      },
      {
        "field": "status",
        "operator": "=",
        "value": "active"
      }
    ]
  }
}
```

生成SQL: `SELECT id,name,age FROM users WHERE (age BETWEEN ? AND ? AND status=?)`

## 2. 条件性查询 (ConditionalQuery)

### 基本结构

```json
{
  "select": ["字段列表"],
  "from": "表名",
  "conditional_query": {
    "if": {
      "field": "条件字段",
      "operator": "操作符",
      "value": "条件值"
    },
    "then": {
      "where": "满足条件时的WHERE子句"
    },
    "else": {
      "where": "不满足条件时的WHERE子句"
    }
  }
}
```

### 示例1: IF-THEN条件

```json
{
  "select": ["id", "name", "age"],
  "from": "users",
  "conditional_query": {
    "if": {
      "field": "age",
      "operator": ">=",
      "value": 18
    },
    "then": {
      "where": {
        "status": "adult"
      }
    }
  }
}
```

生成SQL: `SELECT id,name,age FROM users WHERE (age>=? AND status=?)`

### 示例2: IF-THEN-ELSE条件

```json
{
  "select": ["id", "name"],
  "from": "users",
  "conditional_query": {
    "if": {
      "field": "vip_level",
      "operator": ">",
      "value": 0
    },
    "then": {
      "where": {
        "discount": 0.1
      }
    },
    "else": {
      "where": {
        "discount": 0
      }
    }
  }
}
```

生成SQL: `SELECT id,name FROM users WHERE ((vip_level>? AND discount=?) OR (NOT (vip_level>?) AND discount=?))`

## 3. 完整的复杂查询示例

### 带JOIN的复杂嵌套查询

```json
{
  "select": ["u.id", "u.name", "o.order_no"],
  "from": "users u",
  "join_tables": [
    {
      "type": "LEFT",
      "table": "orders o",
      "condition": {
        "u.id": "o.user_id"
      }
    }
  ],
  "complex_where": {
    "logic": "AND",
    "conditions": [
      {
        "field": "u.status",
        "operator": "=",
        "value": "active"
      },
      {
        "nested": {
          "logic": "OR",
          "conditions": [
            {
              "field": "o.amount",
              "operator": ">",
              "value": 100
            },
            {
              "field": "u.vip_level",
              "operator": ">=",
              "value": 2
            }
          ]
        }
      }
    ]
  },
  "order_by": ["u.id DESC"],
  "limit": 20
}
```

## 4. 数据类型说明

### ComplexCondition 结构

```go
type ComplexCondition struct {
    Logic      string             `json:"logic"`      // "AND" 或 "OR"
    Conditions []ConditionItem    `json:"conditions"` // 条件列表
}
```

### ConditionItem 结构

```go
type ConditionItem struct {
    Field     string            `json:"field,omitempty"`     // 字段名（简单条件）
    Operator  string            `json:"operator,omitempty"`  // 操作符（简单条件）
    Value     any               `json:"value,omitempty"`     // 值（简单条件）
    Nested    *ComplexCondition `json:"nested,omitempty"`    // 嵌套条件
}
```

### ConditionalQuery 结构

```go
type ConditionalQuery struct {
    If        ConditionItem `json:"if"`        // 条件
    Then      QueryPart     `json:"then"`      // 满足条件时的查询部分
    Else      *QueryPart    `json:"else,omitempty"` // 不满足条件时的查询部分（可选）
}
```

## 5. API 函数

### GenerateComplexSQL

处理复杂嵌套条件查询。

```go
func GenerateComplexSQL(jsonStr string) (string, []any, error)
```

### GenerateConditionalSQL

处理条件性查询（IF-THEN-ELSE逻辑）。

```go
func GenerateConditionalSQL(jsonStr string) (string, []any, error)
```

## 6. 错误处理

常见错误类型：

- `BETWEEN操作符需要两个值的数组` - BETWEEN操作符的值必须是包含两个元素的数组
- `不支持的逻辑操作符` - logic字段只支持"AND"和"OR"
- `不支持的操作符` - 使用了不支持的比较操作符
- `字段名不能为空` - 简单条件的field字段不能为空
- `复杂条件不能为空` - ComplexCondition的conditions数组不能为空
