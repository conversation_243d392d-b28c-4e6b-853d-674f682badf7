# 复杂嵌套查询功能增强总结

## 概述

本次增强为 xorm_builder.go 添加了复杂嵌套查询功能，支持 OR、IF、BETWEEN 等高级查询模式，大大提升了查询构建器的灵活性和功能性。

## 新增功能

### 1. 复杂嵌套条件查询 (ComplexCondition)

支持任意深度的 AND/OR 嵌套条件组合。

**新增结构体：**
- `ComplexCondition` - 定义复杂嵌套条件结构
- `ConditionItem` - 条件项，支持简单条件或嵌套条件

**新增函数：**
- `GenerateComplexSQL()` - 处理复杂嵌套条件查询

**示例：**
```json
{
  "select": ["id", "name"],
  "from": "users",
  "complex_where": {
    "logic": "AND",
    "conditions": [
      {
        "field": "status",
        "operator": "=",
        "value": "active"
      },
      {
        "nested": {
          "logic": "OR",
          "conditions": [
            {
              "field": "age",
              "operator": ">",
              "value": 18
            },
            {
              "field": "vip_level",
              "operator": ">=",
              "value": 3
            }
          ]
        }
      }
    ]
  }
}
```

### 2. 条件性查询 (ConditionalQuery)

支持 IF-THEN-ELSE 逻辑的条件性查询构建。

**新增结构体：**
- `ConditionalQuery` - 条件性查询结构
- `QueryPart` - 查询部分

**新增函数：**
- `GenerateConditionalSQL()` - 处理条件性查询

**示例：**
```json
{
  "select": ["id", "name"],
  "from": "users",
  "conditional_query": {
    "if": {
      "field": "vip_level",
      "operator": ">",
      "value": 0
    },
    "then": {
      "where": {
        "discount": 0.1
      }
    },
    "else": {
      "where": {
        "discount": 0
      }
    }
  }
}
```

### 3. 扩展的操作符支持

新增了多个高级操作符：

| 操作符 | 说明 | 示例值 |
|--------|------|--------|
| `BETWEEN` | 范围查询 | `[18, 65]` |
| `NOT IN` | 不包含于 | `["banned", "deleted"]` |
| `NOT LIKE` | 不匹配 | `"%@spam.com"` |
| `IS NOT NULL` | 不为空 | `null` |

### 4. 辅助函数

新增了多个辅助函数来支持复杂查询构建：

- `buildCondition()` - 构建条件，支持简单条件和复杂条件
- `buildComplexCondition()` - 构建复杂嵌套条件
- `buildSimpleCondition()` - 构建简单条件
- `buildConditionalCondition()` - 构建条件性查询条件
- `buildConditionItem()` - 构建条件项
- `buildQueryPartCondition()` - 构建查询部分的条件

## 文件结构

### 新增文件

1. **schema/complex_query_test.go** - 复杂查询功能的测试用例
2. **examples/complex_query_examples.go** - 复杂查询功能的示例代码
3. **docs/complex_query_json_schema.md** - 复杂查询的JSON Schema文档
4. **docs/ENHANCEMENT_SUMMARY.md** - 本总结文档

### 修改文件

1. **schema/xorm_builder.go** - 主要功能实现
2. **schema/README.md** - 更新功能说明

## 测试覆盖

新增了全面的测试用例，包括：

- 基本OR条件测试
- 嵌套AND和OR条件测试
- BETWEEN操作符测试
- NOT IN和NOT LIKE操作符测试
- IS NULL和IS NOT NULL操作符测试
- 复杂嵌套查询与JOIN测试
- 条件性查询测试
- 错误处理测试

所有新功能测试都通过，确保功能的正确性和稳定性。

## 使用示例

### 基本OR条件
```go
sql, args, err := schema.GenerateComplexSQL(`{
  "select": ["id", "name", "age"],
  "from": "users",
  "complex_where": {
    "logic": "OR",
    "conditions": [
      {"field": "age", "operator": ">", "value": 18},
      {"field": "status", "operator": "=", "value": "vip"}
    ]
  }
}`)
// 生成: SELECT id,name,age FROM users WHERE age>? OR status=?
```

### BETWEEN操作符
```go
sql, args, err := schema.GenerateComplexSQL(`{
  "select": ["id", "name", "age"],
  "from": "users",
  "complex_where": {
    "logic": "AND",
    "conditions": [
      {"field": "age", "operator": "BETWEEN", "value": [18, 65]},
      {"field": "status", "operator": "=", "value": "active"}
    ]
  }
}`)
// 生成: SELECT id,name,age FROM users WHERE age BETWEEN ? AND ? AND status=?
```

### 条件性查询
```go
sql, args, err := schema.GenerateConditionalSQL(`{
  "select": ["id", "name"],
  "from": "users",
  "conditional_query": {
    "if": {"field": "age", "operator": ">=", "value": 18},
    "then": {"where": {"status": "adult"}}
  }
}`)
// 生成: SELECT id,name FROM users WHERE age>=? AND status=?
```

## 兼容性

- 完全向后兼容现有API
- 不影响现有功能的使用
- 新功能通过新的函数和结构体提供

## 性能考虑

- 使用高效的条件构建算法
- 避免不必要的内存分配
- 保持与xorm builder库的最佳实践一致

## 总结

本次增强成功为xorm_builder.go添加了强大的复杂嵌套查询功能，包括：

1. ✅ OR条件支持
2. ✅ IF条件支持 (条件性查询)
3. ✅ BETWEEN操作符
4. ✅ 嵌套条件支持
5. ✅ 扩展的操作符集合
6. ✅ 完整的测试覆盖
7. ✅ 详细的文档和示例

这些功能大大增强了查询构建器的灵活性，使其能够处理更复杂的业务场景和查询需求。
