# Schema 模块使用说明文档

## 概述

Schema 模块是一个强大的 SQL 构建器，基于 JSON 配置生成各种 SQL 语句。它使用 xorm/builder 库作为底层引擎，提供了类型安全、参数化查询和防 SQL 注入的功能。

## 功能特性

### 🔍 查询功能
- **基本查询** - SELECT、WHERE、JOIN、GROUP BY、HAVING、ORDER BY、LIMIT
- **高级查询** - 支持复杂操作符（>、<、IN、LIKE、BETWEEN、IS NULL 等）
- **复杂嵌套查询** - 支持任意深度的 AND/OR 嵌套条件
- **条件性查询** - 支持 IF-THEN-ELSE 逻辑

### 📝 数据操作
- **插入操作** - 单条插入、批量插入
- **更新操作** - 条件更新
- **删除操作** - 条件删除

### 🏗️ DDL 操作
- **创建表** - 完整的表结构定义
- **创建索引** - 普通索引、唯一索引
- **修改表** - 添加/删除/修改列、约束管理

## 安装依赖

```bash
go get xorm.io/builder
go get github.com/stretchr/testify/assert  # 用于测试
```

## API 函数列表

### 查询相关
| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `GenerateSQLFromJSON(jsonStr)` | 基本查询构建 | `(sql, args, error)` |
| `GenerateAdvancedSQL(jsonStr)` | 高级查询构建 | `(sql, args, error)` |
| `GenerateComplexSQL(jsonStr)` | 复杂嵌套查询 | `(sql, args, error)` |
| `GenerateConditionalSQL(jsonStr)` | 条件性查询 | `(sql, args, error)` |

### 数据操作相关
| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `GenerateInsertSQL(jsonStr)` | 插入语句 | `(sql, args, error)` |
| `GenerateUpdateSQL(jsonStr)` | 更新语句 | `(sql, args, error)` |
| `GenerateDeleteSQL(jsonStr)` | 删除语句 | `(sql, args, error)` |
| `GenerateBatchInsertSQL(jsonStr)` | 批量插入 | `(sql, args, error)` |

### DDL 相关
| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `GenerateCreateTableSQL(jsonStr)` | 创建表 | `(sql, error)` |
| `GenerateCreateIndexSQL(jsonStr)` | 创建索引 | `(sql, error)` |
| `GenerateAlterTableSQL(jsonStr)` | 修改表 | `([]sql, error)` |

## 详细使用指南

### 1. 基本查询

#### 1.1 简单查询
```go
package main

import (
    "fmt"
    "log"
    "your-project/schema"
)

func basicQuery() {
    jsonStr := `{
        "select": ["id", "name", "email", "created_at"],
        "from": "users",
        "where": {
            "status": "active",
            "age": 25
        },
        "order_by": ["created_at DESC"],
        "limit": 10,
        "offset": 0
    }`

    sql, args, err := schema.GenerateSQLFromJSON(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
    // 输出: SELECT id,name,email,created_at FROM users WHERE age=? AND status=? ORDER BY created_at DESC LIMIT 10
    // Args: [25 active]
}
```

#### 1.2 JOIN 查询
```go
func joinQuery() {
    jsonStr := `{
        "select": ["u.id", "u.name", "p.title", "c.name as category"],
        "from": "users u",
        "join_tables": [
            {
                "type": "LEFT",
                "table": "posts p",
                "condition": {
                    "u.id": "p.user_id"
                }
            },
            {
                "type": "INNER",
                "table": "categories c",
                "condition": {
                    "p.category_id": "c.id"
                }
            }
        ],
        "where": {
            "u.status": "active",
            "p.published": true
        },
        "group_by": ["u.id"],
        "having": {
            "COUNT(p.id)": 1
        },
        "order_by": ["u.name ASC"],
        "limit": 20
    }`

    sql, args, err := schema.GenerateSQLFromJSON(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
}
```

### 2. 高级查询

#### 2.1 高级操作符
```go
func advancedQuery() {
    jsonStr := `{
        "select": ["id", "name", "age", "salary"],
        "from": "employees",
        "adv_where": {
            "age": {
                "op": "BETWEEN",
                "v": [25, 45]
            },
            "salary": {
                "op": ">=",
                "v": 50000
            },
            "department": {
                "op": "IN",
                "v": ["IT", "Engineering", "Product"]
            },
            "name": {
                "op": "LIKE",
                "v": "%John%"
            },
            "deleted_at": {
                "op": "IS NULL",
                "v": null
            }
        },
        "order_by": ["salary DESC"],
        "limit": 50
    }`

    sql, args, err := schema.GenerateAdvancedSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
}
```

### 3. 复杂嵌套查询

#### 3.1 OR 条件查询
```go
func complexOrQuery() {
    jsonStr := `{
        "select": ["id", "name", "email"],
        "from": "users",
        "complex_where": {
            "logic": "OR",
            "conditions": [
                {
                    "field": "vip_level",
                    "operator": ">=",
                    "value": 3
                },
                {
                    "field": "total_orders",
                    "operator": ">",
                    "value": 100
                },
                {
                    "field": "email",
                    "operator": "LIKE",
                    "value": "%@vip.com"
                }
            ]
        }
    }`

    sql, args, err := schema.GenerateComplexSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    // 输出: SELECT id,name,email FROM users WHERE vip_level>=? OR total_orders>? OR email LIKE ?
}
```

#### 3.2 嵌套 AND/OR 查询
```go
func nestedQuery() {
    jsonStr := `{
        "select": ["id", "name", "status"],
        "from": "users",
        "complex_where": {
            "logic": "AND",
            "conditions": [
                {
                    "field": "status",
                    "operator": "=",
                    "value": "active"
                },
                {
                    "nested": {
                        "logic": "OR",
                        "conditions": [
                            {
                                "field": "age",
                                "operator": "BETWEEN",
                                "value": [18, 30]
                            },
                            {
                                "nested": {
                                    "logic": "AND",
                                    "conditions": [
                                        {
                                            "field": "vip_level",
                                            "operator": ">=",
                                            "value": 2
                                        },
                                        {
                                            "field": "last_login",
                                            "operator": "IS NOT NULL",
                                            "value": null
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                }
            ]
        }
    }`

    sql, args, err := schema.GenerateComplexSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    // 输出: SELECT id,name,status FROM users WHERE status=? AND (age BETWEEN ? AND ? OR (vip_level>=? AND last_login IS NOT NULL))
}
```

### 4. 条件性查询 (IF-THEN-ELSE)

#### 4.1 基本条件查询
```go
func conditionalQuery() {
    jsonStr := `{
        "select": ["id", "name", "discount_rate"],
        "from": "customers",
        "conditional_query": {
            "if": {
                "field": "membership_level",
                "operator": ">=",
                "value": 3
            },
            "then": {
                "where": {
                    "discount_rate": 0.15
                }
            },
            "else": {
                "where": {
                    "discount_rate": 0.05
                }
            }
        }
    }`

    sql, args, err := schema.GenerateConditionalSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    // 输出: SELECT id,name,discount_rate FROM customers WHERE (membership_level>=? AND discount_rate=?) OR (NOT membership_level>=? AND discount_rate=?)
}
```

### 5. 数据操作

#### 5.1 插入数据
```go
func insertData() {
    // 单条插入
    jsonStr := `{
        "table": "users",
        "values": {
            "name": "张三",
            "email": "<EMAIL>",
            "age": 28,
            "status": "active",
            "created_at": "2024-01-01 10:00:00"
        }
    }`

    sql, args, err := schema.GenerateInsertSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
    // 输出: INSERT INTO users (age,created_at,email,name,status) Values (?,?,?,?,?)
}
```

#### 5.2 批量插入
```go
func batchInsert() {
    jsonStr := `{
        "table": "products",
        "columns": ["name", "price", "category_id", "stock"],
        "values": [
            ["iPhone 15", 999.99, 1, 100],
            ["Samsung Galaxy", 899.99, 1, 150],
            ["MacBook Pro", 1999.99, 2, 50],
            ["Dell XPS", 1299.99, 2, 75]
        ]
    }`

    sql, args, err := schema.GenerateBatchInsertSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
    // 输出: INSERT INTO products (name, price, category_id, stock) VALUES (?, ?, ?, ?), (?, ?, ?, ?), (?, ?, ?, ?), (?, ?, ?, ?)
}
```

#### 5.3 更新数据
```go
func updateData() {
    jsonStr := `{
        "table": "users",
        "values": {
            "last_login": "2024-01-15 14:30:00",
            "login_count": 25,
            "status": "active"
        },
        "where": {
            "id": 123,
            "email": "<EMAIL>"
        }
    }`

    sql, args, err := schema.GenerateUpdateSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
    // 输出: UPDATE users SET last_login=?,login_count=?,status=? WHERE email=? AND id=?
}
```

#### 5.4 删除数据
```go
func deleteData() {
    jsonStr := `{
        "table": "users",
        "where": {
            "status": "inactive",
            "last_login": "2023-01-01"
        }
    }`

    sql, args, err := schema.GenerateDeleteSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SQL: %s\n", sql)
    fmt.Printf("Args: %v\n", args)
    // 输出: DELETE FROM users WHERE last_login=? AND status=?
}
```

### 6. DDL 操作

#### 6.1 创建表
```go
func createTable() {
    jsonStr := `{
        "name": "orders",
        "columns": {
            "id": "bigint(20) NOT NULL AUTO_INCREMENT",
            "user_id": "bigint(20) NOT NULL",
            "product_id": "bigint(20) NOT NULL",
            "quantity": "int(11) NOT NULL DEFAULT 1",
            "price": "decimal(10,2) NOT NULL",
            "status": "enum('pending','paid','shipped','delivered','cancelled') DEFAULT 'pending'",
            "order_date": "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP",
            "updated_at": "timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP"
        },
        "constraints": {
            "PRIMARY": "PRIMARY KEY (id)",
            "fk_user": "FOREIGN KEY (user_id) REFERENCES users(id)",
            "fk_product": "FOREIGN KEY (product_id) REFERENCES products(id)",
            "uk_order": "UNIQUE KEY (user_id, product_id, order_date)"
        },
        "engine": "InnoDB",
        "charset": "utf8mb4",
        "collate": "utf8mb4_unicode_ci",
        "comment": "订单表",
        "if_not_exists": true
    }`

    sql, err := schema.GenerateCreateTableSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("SQL: %s\n", sql)
}
```

#### 6.2 创建索引
```go
func createIndex() {
    // 普通索引
    jsonStr := `{
        "name": "idx_user_status",
        "table": "users",
        "columns": ["status", "created_at"],
        "unique": false,
        "if_not_exists": true
    }`

    sql, err := schema.GenerateCreateIndexSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("普通索引: %s\n", sql)

    // 唯一索引
    uniqueIndexJSON := `{
        "name": "uk_email",
        "table": "users",
        "columns": ["email"],
        "unique": true,
        "if_not_exists": true
    }`

    sql, err = schema.GenerateCreateIndexSQL(uniqueIndexJSON)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("唯一索引: %s\n", sql)
}
```

#### 6.3 修改表结构
```go
func alterTable() {
    jsonStr := `{
        "name": "users",
        "add_columns": {
            "phone": "varchar(20) DEFAULT NULL COMMENT '手机号'",
            "address": "text DEFAULT NULL COMMENT '地址'",
            "avatar": "varchar(255) DEFAULT NULL COMMENT '头像URL'"
        },
        "drop_columns": ["old_field", "deprecated_column"],
        "modify_columns": {
            "name": "varchar(100) NOT NULL COMMENT '用户名'",
            "email": "varchar(320) NOT NULL COMMENT '邮箱地址'"
        },
        "rename_columns": {
            "old_email": "email_address",
            "old_phone": "mobile"
        },
        "add_constraints": {
            "idx_phone": "INDEX (phone)",
            "idx_status_created": "INDEX (status, created_at)",
            "uk_phone": "UNIQUE KEY (phone)"
        },
        "drop_constraints": ["old_index", "deprecated_constraint"]
    }`

    statements, err := schema.GenerateAlterTableSQL(jsonStr)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Println("修改表SQL语句:")
    for i, stmt := range statements {
        fmt.Printf("%d. %s\n", i+1, stmt)
    }
}
```

## 支持的操作符

### 基本操作符
| 操作符 | 说明 | JSON 示例 |
|--------|------|-----------|
| `=` | 等于 | `{"op": "=", "v": "active"}` |
| `>` | 大于 | `{"op": ">", "v": 18}` |
| `<` | 小于 | `{"op": "<", "v": 65}` |
| `>=` | 大于等于 | `{"op": ">=", "v": 18}` |
| `<=` | 小于等于 | `{"op": "<=", "v": 65}` |
| `!=`, `<>` | 不等于 | `{"op": "!=", "v": 0}` |

### 高级操作符
| 操作符 | 说明 | JSON 示例 |
|--------|------|-----------|
| `IN` | 包含于 | `{"op": "IN", "v": ["active", "pending"]}` |
| `NOT IN` | 不包含于 | `{"op": "NOT IN", "v": ["banned", "deleted"]}` |
| `LIKE` | 模糊匹配 | `{"op": "LIKE", "v": "%john%"}` |
| `NOT LIKE` | 不匹配 | `{"op": "NOT LIKE", "v": "%@spam.com"}` |
| `BETWEEN` | 范围查询 | `{"op": "BETWEEN", "v": [18, 65]}` |
| `IS NULL` | 为空 | `{"op": "IS NULL", "v": null}` |
| `IS NOT NULL` | 不为空 | `{"op": "IS NOT NULL", "v": null}` |

## 数据结构定义

### 查询相关结构
```go
// 基本查询结构
type SQLSchema struct {
    Select     []string       `json:"select"`
    From       string         `json:"from"`
    Where      map[string]any `json:"where"`
    OrderBy    []string       `json:"order_by"`
    GroupBy    []string       `json:"group_by"`
    Having     map[string]any `json:"having"`
    Limit      int            `json:"limit"`
    Offset     int            `json:"offset"`
    JoinTables []JoinTable    `json:"join_tables"`
}

// JOIN 表结构
type JoinTable struct {
    Type      string         `json:"type"` // LEFT, RIGHT, INNER
    Table     string         `json:"table"`
    Condition map[string]any `json:"condition"`
}

// 高级条件结构
type AdvancedCondition struct {
    Operator string `json:"op"` // 操作符
    Value    any    `json:"v"`  // 值
}

// 复杂条件结构
type ComplexCondition struct {
    Logic      string             `json:"logic"`      // AND, OR
    Conditions []ConditionItem    `json:"conditions"` // 条件列表
}

// 条件项
type ConditionItem struct {
    Field     string            `json:"field,omitempty"`     // 字段名
    Operator  string            `json:"operator,omitempty"`  // 操作符
    Value     any               `json:"value,omitempty"`     // 值
    Nested    *ComplexCondition `json:"nested,omitempty"`    // 嵌套条件
}
```

### DDL 相关结构
```go
// 表结构定义
type TableSchema struct {
    Name        string            `json:"name"`
    Columns     map[string]string `json:"columns"`
    Constraints map[string]string `json:"constraints"`
    Engine      string            `json:"engine"`
    Charset     string            `json:"charset"`
    Collate     string            `json:"collate"`
    Comment     string            `json:"comment"`
    IfNotExists bool              `json:"if_not_exists"`
}

// 索引结构定义
type IndexSchema struct {
    Name        string   `json:"name"`
    Table       string   `json:"table"`
    Columns     []string `json:"columns"`
    Unique      bool     `json:"unique"`
    IfNotExists bool     `json:"if_not_exists"`
}
```

## 错误处理

### 常见错误类型
```go
// 查询相关错误
- "select字段不能为空"
- "from字段不能为空"
- "不支持的JOIN类型: %s"
- "不支持的操作符: %s"

// 条件相关错误
- "BETWEEN操作符需要两个值的数组"
- "不支持的逻辑操作符: %s"
- "字段名不能为空"
- "复杂条件不能为空"

// DDL 相关错误
- "表名不能为空"
- "列定义不能为空"
- "索引名不能为空"
- "索引列不能为空"
```

### 错误处理示例
```go
func handleErrors() {
    jsonStr := `{
        "select": [],
        "from": "users"
    }`

    sql, args, err := schema.GenerateSQLFromJSON(jsonStr)
    if err != nil {
        switch {
        case strings.Contains(err.Error(), "select字段不能为空"):
            fmt.Println("请提供要查询的字段")
        case strings.Contains(err.Error(), "from字段不能为空"):
            fmt.Println("请提供要查询的表名")
        default:
            fmt.Printf("未知错误: %v\n", err)
        }
        return
    }

    fmt.Printf("SQL: %s, Args: %v\n", sql, args)
}
```

## 最佳实践

### 1. 安全性
```go
// ✅ 推荐：使用参数化查询
jsonStr := `{
    "select": ["id", "name"],
    "from": "users",
    "where": {
        "status": "active",
        "age": 25
    }
}`

// ❌ 避免：直接拼接用户输入
// 本模块已自动处理参数化，无需担心 SQL 注入
```

### 2. 性能优化
```go
// ✅ 推荐：使用索引字段进行查询
jsonStr := `{
    "select": ["id", "name"],
    "from": "users",
    "where": {
        "email": "<EMAIL>"  // email 字段有索引
    }
}`

// ✅ 推荐：限制查询结果数量
jsonStr := `{
    "select": ["*"],
    "from": "large_table",
    "limit": 100,
    "offset": 0
}`
```

### 3. 复杂查询优化
```go
// ✅ 推荐：合理使用嵌套条件
jsonStr := `{
    "select": ["id", "name"],
    "from": "users",
    "complex_where": {
        "logic": "AND",
        "conditions": [
            {
                "field": "status",
                "operator": "=",
                "value": "active"
            },
            {
                "nested": {
                    "logic": "OR",
                    "conditions": [
                        {"field": "vip_level", "operator": ">=", "value": 3},
                        {"field": "total_orders", "operator": ">", "value": 100}
                    ]
                }
            }
        ]
    }
}`
```

## 完整示例

### 电商系统查询示例
```go
package main

import (
    "fmt"
    "log"
    "your-project/schema"
)

func ecommerceExample() {
    // 查询活跃用户的订单信息
    queryJSON := `{
        "select": [
            "u.id as user_id",
            "u.name as user_name",
            "u.email",
            "o.id as order_id",
            "o.total_amount",
            "o.status as order_status",
            "COUNT(oi.id) as item_count"
        ],
        "from": "users u",
        "join_tables": [
            {
                "type": "INNER",
                "table": "orders o",
                "condition": {
                    "u.id": "o.user_id"
                }
            },
            {
                "type": "LEFT",
                "table": "order_items oi",
                "condition": {
                    "o.id": "oi.order_id"
                }
            }
        ],
        "complex_where": {
            "logic": "AND",
            "conditions": [
                {
                    "field": "u.status",
                    "operator": "=",
                    "value": "active"
                },
                {
                    "field": "o.created_at",
                    "operator": "BETWEEN",
                    "value": ["2024-01-01", "2024-12-31"]
                },
                {
                    "nested": {
                        "logic": "OR",
                        "conditions": [
                            {
                                "field": "o.total_amount",
                                "operator": ">=",
                                "value": 100
                            },
                            {
                                "field": "u.vip_level",
                                "operator": ">=",
                                "value": 2
                            }
                        ]
                    }
                }
            ]
        },
        "group_by": ["u.id", "o.id"],
        "having": {
            "COUNT(oi.id)": 1
        },
        "order_by": ["o.created_at DESC"],
        "limit": 50
    }`

    sql, args, err := schema.GenerateComplexSQL(queryJSON)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("电商查询SQL:\n%s\n", sql)
    fmt.Printf("参数: %v\n", args)
}

func main() {
    ecommerceExample()
}
```

## 总结

Schema 模块提供了强大而灵活的 SQL 构建能力，支持从简单查询到复杂嵌套条件的各种场景。通过 JSON 配置的方式，使得 SQL 构建更加直观和可维护，同时保证了类型安全和防 SQL 注入的特性。

### 主要优势
- 🔒 **安全性** - 自动参数化查询，防止 SQL 注入
- 🎯 **类型安全** - 基于结构体的强类型定义
- 🔧 **灵活性** - 支持复杂嵌套条件和条件性查询
- 📝 **易维护** - JSON 配置方式，便于理解和修改
- 🚀 **高性能** - 基于 xorm/builder 的高效实现

### 适用场景
- 动态查询构建
- 复杂报表查询
- 条件性业务逻辑
- 数据库迁移和初始化
- API 查询参数转换
```
