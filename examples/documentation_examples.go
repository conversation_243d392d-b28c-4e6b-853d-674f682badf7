package main

import (
	"fmt"
	"log"

	"xorm-json-builder/schema"
)

func main() {
	fmt.Println("=== Schema 模块功能演示 ===\n")

	// 1. 基本查询
	basicQueryExample()

	// 2. JOIN 查询
	joinQueryExample()

	// 3. 高级查询
	advancedQueryExample()

	// 4. 复杂嵌套查询
	complexQueryExample()

	// 5. 条件性查询
	conditionalQueryExample()

	// 6. 数据操作
	dataOperationExamples()

	// 7. DDL 操作
	ddlExamples()
}

func basicQueryExample() {
	fmt.Println("1. 基本查询示例:")
	jsonStr := `{
		"select": ["id", "name", "email", "created_at"],
		"from": "users",
		"where": {
			"status": "active",
			"age": 25
		},
		"order_by": ["created_at DESC"],
		"limit": 10,
		"offset": 0
	}`

	sql, args, err := schema.GenerateSQLFromJSON(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}

	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func joinQueryExample() {
	fmt.Println("2. JOIN 查询示例:")
	jsonStr := `{
		"select": ["u.id", "u.name", "p.title", "c.name as category"],
		"from": "users u",
		"join_tables": [
			{
				"type": "LEFT",
				"table": "posts p",
				"condition": {
					"u.id": "p.user_id"
				}
			},
			{
				"type": "INNER",
				"table": "categories c",
				"condition": {
					"p.category_id": "c.id"
				}
			}
		],
		"where": {
			"u.status": "active"
		},
		"group_by": ["u.id"],
		"order_by": ["u.name ASC"],
		"limit": 20
	}`

	sql, args, err := schema.GenerateSQLFromJSON(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}

	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func advancedQueryExample() {
	fmt.Println("3. 高级查询示例:")
	jsonStr := `{
		"select": ["id", "name", "age", "salary"],
		"from": "employees",
		"adv_where": {
			"age": {
				"op": "BETWEEN",
				"v": [25, 45]
			},
			"salary": {
				"op": ">=",
				"v": 50000
			},
			"department": {
				"op": "IN",
				"v": ["IT", "Engineering", "Product"]
			},
			"name": {
				"op": "LIKE",
				"v": "%John%"
			},
			"deleted_at": {
				"op": "IS NULL",
				"v": null
			}
		},
		"order_by": ["salary DESC"],
		"limit": 50
	}`

	sql, args, err := schema.GenerateAdvancedSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}

	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func complexQueryExample() {
	fmt.Println("4. 复杂嵌套查询示例:")
	jsonStr := `{
		"select": ["id", "name", "status"],
		"from": "users",
		"complex_where": {
			"logic": "AND",
			"conditions": [
				{
					"field": "status",
					"operator": "=",
					"value": "active"
				},
				{
					"nested": {
						"logic": "OR",
						"conditions": [
							{
								"field": "age",
								"operator": "BETWEEN",
								"value": [18, 30]
							},
							{
								"nested": {
									"logic": "AND",
									"conditions": [
										{
											"field": "vip_level",
											"operator": ">=",
											"value": 2
										},
										{
											"field": "last_login",
											"operator": "IS NOT NULL",
											"value": null
										}
									]
								}
							}
						]
					}
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}

	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func conditionalQueryExample() {
	fmt.Println("5. 条件性查询示例:")
	jsonStr := `{
		"select": ["id", "name", "discount_rate"],
		"from": "customers",
		"conditional_query": {
			"if": {
				"field": "membership_level",
				"operator": ">=",
				"value": 3
			},
			"then": {
				"where": {
					"discount_rate": 0.15
				}
			},
			"else": {
				"where": {
					"discount_rate": 0.05
				}
			}
		}
	}`

	sql, args, err := schema.GenerateConditionalSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}

	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func dataOperationExamples() {
	fmt.Println("6. 数据操作示例:")

	// 插入数据
	fmt.Println("6.1 插入数据:")
	insertJSON := `{
		"table": "users",
		"values": {
			"name": "张三",
			"email": "<EMAIL>",
			"age": 28,
			"status": "active"
		}
	}`

	sql, args, err := schema.GenerateInsertSQL(insertJSON)
	if err != nil {
		log.Printf("插入错误: %v", err)
	} else {
		fmt.Printf("插入SQL: %s\n", sql)
		fmt.Printf("参数: %v\n", args)
	}

	// 批量插入
	fmt.Println("\n6.2 批量插入:")
	batchInsertJSON := `{
		"table": "products",
		"columns": ["name", "price", "category_id", "stock"],
		"values": [
			["iPhone 15", 999.99, 1, 100],
			["Samsung Galaxy", 899.99, 1, 150],
			["MacBook Pro", 1999.99, 2, 50]
		]
	}`

	sql, args, err = schema.GenerateBatchInsertSQL(batchInsertJSON)
	if err != nil {
		log.Printf("批量插入错误: %v", err)
	} else {
		fmt.Printf("批量插入SQL: %s\n", sql)
		fmt.Printf("参数: %v\n", args)
	}

	// 更新数据
	fmt.Println("\n6.3 更新数据:")
	updateJSON := `{
		"table": "users",
		"values": {
			"last_login": "2024-01-15 14:30:00",
			"login_count": 25
		},
		"where": {
			"id": 123
		}
	}`

	sql, args, err = schema.GenerateUpdateSQL(updateJSON)
	if err != nil {
		log.Printf("更新错误: %v", err)
	} else {
		fmt.Printf("更新SQL: %s\n", sql)
		fmt.Printf("参数: %v\n", args)
	}

	// 删除数据
	fmt.Println("\n6.4 删除数据:")
	deleteJSON := `{
		"table": "users",
		"where": {
			"status": "inactive"
		}
	}`

	sql, args, err = schema.GenerateDeleteSQL(deleteJSON)
	if err != nil {
		log.Printf("删除错误: %v", err)
	} else {
		fmt.Printf("删除SQL: %s\n", sql)
		fmt.Printf("参数: %v\n\n", args)
	}
}

func ddlExamples() {
	fmt.Println("7. DDL 操作示例:")

	// 创建表
	fmt.Println("7.1 创建表:")
	createTableJSON := `{
		"name": "orders",
		"columns": {
			"id": "bigint(20) NOT NULL AUTO_INCREMENT",
			"user_id": "bigint(20) NOT NULL",
			"total_amount": "decimal(10,2) NOT NULL",
			"status": "enum('pending','paid','shipped') DEFAULT 'pending'",
			"created_at": "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP"
		},
		"constraints": {
			"PRIMARY": "PRIMARY KEY (id)",
			"fk_user": "FOREIGN KEY (user_id) REFERENCES users(id)"
		},
		"engine": "InnoDB",
		"charset": "utf8mb4",
		"comment": "订单表",
		"if_not_exists": true
	}`

	sql, err := schema.GenerateCreateTableSQL(createTableJSON)
	if err != nil {
		log.Printf("创建表错误: %v", err)
	} else {
		fmt.Printf("创建表SQL:\n%s\n", sql)
	}

	// 创建索引
	fmt.Println("\n7.2 创建索引:")
	createIndexJSON := `{
		"name": "idx_user_status",
		"table": "orders",
		"columns": ["user_id", "status"],
		"unique": false,
		"if_not_exists": true
	}`

	sql, err = schema.GenerateCreateIndexSQL(createIndexJSON)
	if err != nil {
		log.Printf("创建索引错误: %v", err)
	} else {
		fmt.Printf("创建索引SQL: %s\n", sql)
	}

	// 修改表
	fmt.Println("\n7.3 修改表:")
	alterTableJSON := `{
		"name": "orders",
		"add_columns": {
			"shipping_address": "text DEFAULT NULL COMMENT '收货地址'"
		},
		"modify_columns": {
			"total_amount": "decimal(12,2) NOT NULL COMMENT '订单总金额'"
		},
		"add_constraints": {
			"idx_created": "INDEX (created_at)"
		}
	}`

	statements, err := schema.GenerateAlterTableSQL(alterTableJSON)
	if err != nil {
		log.Printf("修改表错误: %v", err)
	} else {
		fmt.Println("修改表SQL:")
		for i, stmt := range statements {
			fmt.Printf("%d. %s\n", i+1, stmt)
		}
	}
}
