package main

import (
	"fmt"
	"log"

	"xorm-json-builder/schema"
)

func main() {
	fmt.Println("=== 复杂嵌套查询示例 ===\n")

	// 示例1: 基本OR条件查询
	example1()

	// 示例2: 嵌套AND和OR条件
	example2()

	// 示例3: BETWEEN操作符
	example3()

	// 示例4: NOT IN和NOT LIKE操作符
	example4()

	// 示例5: IS NULL和IS NOT NULL操作符
	example5()

	// 示例6: 复杂嵌套查询与JOIN
	example6()

	// 示例7: 条件性查询 (IF-THEN)
	example7()

	// 示例8: 条件性查询 (IF-THEN-ELSE)
	example8()
}

func example1() {
	fmt.Println("1. 基本OR条件查询:")
	jsonStr := `{
		"select": ["id", "name", "age"],
		"from": "users",
		"complex_where": {
			"logic": "OR",
			"conditions": [
				{
					"field": "age",
					"operator": ">",
					"value": 18
				},
				{
					"field": "status",
					"operator": "=",
					"value": "vip"
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example2() {
	fmt.Println("2. 嵌套AND和OR条件:")
	jsonStr := `{
		"select": ["id", "name"],
		"from": "users",
		"complex_where": {
			"logic": "AND",
			"conditions": [
				{
					"field": "status",
					"operator": "=",
					"value": "active"
				},
				{
					"nested": {
						"logic": "OR",
						"conditions": [
							{
								"field": "age",
								"operator": ">",
								"value": 18
							},
							{
								"field": "vip_level",
								"operator": ">=",
								"value": 3
							}
						]
					}
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example3() {
	fmt.Println("3. BETWEEN操作符:")
	jsonStr := `{
		"select": ["id", "name", "age"],
		"from": "users",
		"complex_where": {
			"logic": "AND",
			"conditions": [
				{
					"field": "age",
					"operator": "BETWEEN",
					"value": [18, 65]
				},
				{
					"field": "status",
					"operator": "=",
					"value": "active"
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example4() {
	fmt.Println("4. NOT IN和NOT LIKE操作符:")
	jsonStr := `{
		"select": ["id", "name"],
		"from": "users",
		"complex_where": {
			"logic": "AND",
			"conditions": [
				{
					"field": "status",
					"operator": "NOT IN",
					"value": ["banned", "deleted"]
				},
				{
					"field": "email",
					"operator": "NOT LIKE",
					"value": "%@spam.com"
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example5() {
	fmt.Println("5. IS NULL和IS NOT NULL操作符:")
	jsonStr := `{
		"select": ["id", "name"],
		"from": "users",
		"complex_where": {
			"logic": "OR",
			"conditions": [
				{
					"field": "deleted_at",
					"operator": "IS NULL",
					"value": null
				},
				{
					"field": "email",
					"operator": "IS NOT NULL",
					"value": null
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example6() {
	fmt.Println("6. 复杂嵌套查询与JOIN:")
	jsonStr := `{
		"select": ["u.id", "u.name", "o.order_no"],
		"from": "users u",
		"join_tables": [
			{
				"type": "LEFT",
				"table": "orders o",
				"condition": {
					"u.id": "o.user_id"
				}
			}
		],
		"complex_where": {
			"logic": "AND",
			"conditions": [
				{
					"field": "u.status",
					"operator": "=",
					"value": "active"
				},
				{
					"nested": {
						"logic": "OR",
						"conditions": [
							{
								"field": "o.amount",
								"operator": ">",
								"value": 100
							},
							{
								"field": "u.vip_level",
								"operator": ">=",
								"value": 2
							}
						]
					}
				}
			]
		}
	}`

	sql, args, err := schema.GenerateComplexSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example7() {
	fmt.Println("7. 条件性查询 (IF-THEN):")
	jsonStr := `{
		"select": ["id", "name", "age"],
		"from": "users",
		"conditional_query": {
			"if": {
				"field": "age",
				"operator": ">=",
				"value": 18
			},
			"then": {
				"where": {
					"status": "adult"
				}
			}
		}
	}`

	sql, args, err := schema.GenerateConditionalSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}

func example8() {
	fmt.Println("8. 条件性查询 (IF-THEN-ELSE):")
	jsonStr := `{
		"select": ["id", "name"],
		"from": "users",
		"conditional_query": {
			"if": {
				"field": "vip_level",
				"operator": ">",
				"value": 0
			},
			"then": {
				"where": {
					"discount": 0.1
				}
			},
			"else": {
				"where": {
					"discount": 0
				}
			}
		}
	}`

	sql, args, err := schema.GenerateConditionalSQL(jsonStr)
	if err != nil {
		log.Printf("错误: %v", err)
		return
	}
	fmt.Printf("SQL: %s\n", sql)
	fmt.Printf("参数: %v\n\n", args)
}
