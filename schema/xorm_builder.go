package schema

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"xorm.io/builder"
)

// SQLSchema 定义SQL查询的JSON Schema结构
type SQLSchema struct {
	Select     []string       `json:"select"`
	From       string         `json:"from"`
	Where      map[string]any `json:"where"`
	OrderBy    []string       `json:"order_by"`
	GroupBy    []string       `json:"group_by"`
	Having     map[string]any `json:"having"`
	Limit      int            `json:"limit"`
	Offset     int            `json:"offset"`
	JoinTables []JoinTable    `json:"join_tables"`
}

// JoinTable 定义JOIN表结构
type JoinTable struct {
	Type      string         `json:"type"` // LEFT, RIGHT, INNER
	Table     string         `json:"table"`
	Condition map[string]any `json:"condition"`
}

// AdvancedCondition 定义更复杂的条件结构
type AdvancedCondition struct {
	Operator string `json:"op"` // =, >, <, IN, LIKE, BETWEEN, IS NULL, IS NOT NULL 等
	Value    any    `json:"v"`
}

// ComplexCondition 定义复杂嵌套条件结构
type ComplexCondition struct {
	Logic      string          `json:"logic"`      // AND, OR
	Conditions []ConditionItem `json:"conditions"` // 条件列表
}

// ConditionItem 条件项，可以是简单条件或嵌套条件
type ConditionItem struct {
	Field    string            `json:"field,omitempty"`    // 字段名（简单条件）
	Operator string            `json:"operator,omitempty"` // 操作符（简单条件）
	Value    any               `json:"value,omitempty"`    // 值（简单条件）
	Nested   *ComplexCondition `json:"nested,omitempty"`   // 嵌套条件
}

// ConditionalQuery 条件性查询结构
type ConditionalQuery struct {
	If   ConditionItem `json:"if"`             // 条件
	Then QueryPart     `json:"then"`           // 满足条件时的查询部分
	Else *QueryPart    `json:"else,omitempty"` // 不满足条件时的查询部分（可选）
}

// QueryPart 查询部分
type QueryPart struct {
	Select  []string `json:"select,omitempty"`
	Where   any      `json:"where,omitempty"` // 可以是简单条件或复杂条件
	OrderBy []string `json:"order_by,omitempty"`
	GroupBy []string `json:"group_by,omitempty"`
	Having  any      `json:"having,omitempty"`
	Limit   int      `json:"limit,omitempty"`
	Offset  int      `json:"offset,omitempty"`
}

// WhereClause 支持复杂的WHERE条件
type WhereClause map[string]AdvancedCondition

// GenerateSQLFromJSON 从JSON字符串生成SQL
func GenerateSQLFromJSON(jsonStr string) (string, []any, error) {
	var schema SQLSchema
	if err := json.Unmarshal([]byte(jsonStr), &schema); err != nil {
		return "", nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	return GenerateSQLFromSchema(schema)
}

// GenerateSQLFromSchema 从Schema结构生成SQL
func GenerateSQLFromSchema(schema SQLSchema) (string, []any, error) {
	// 验证必要字段
	if len(schema.Select) == 0 {
		return "", nil, fmt.Errorf("select字段不能为空")
	}
	if schema.From == "" {
		return "", nil, fmt.Errorf("from字段不能为空")
	}

	// 构建查询
	query := builder.Dialect(builder.MYSQL).Select(schema.Select...)
	query = query.From(schema.From)

	// 处理 JOIN
	for _, join := range schema.JoinTables {
		switch join.Type {
		case "LEFT":
			query = query.LeftJoin(join.Table, builder.Eq(join.Condition))
		case "RIGHT":
			query = query.RightJoin(join.Table, builder.Eq(join.Condition))
		case "INNER":
			query = query.InnerJoin(join.Table, builder.Eq(join.Condition))
		default:
			return "", nil, fmt.Errorf("不支持的JOIN类型: %s", join.Type)
		}
	}

	// 处理 WHERE 条件
	if len(schema.Where) > 0 {
		query = query.Where(builder.Eq(schema.Where))
	}

	// 处理 GROUP BY
	if len(schema.GroupBy) > 0 {
		for _, field := range schema.GroupBy {
			query = query.GroupBy(field)
		}
	}

	// 处理 HAVING
	if len(schema.Having) > 0 {
		query = query.Having(builder.Eq(schema.Having))
	}

	// 处理 ORDER BY
	if len(schema.OrderBy) > 0 {
		for _, field := range schema.OrderBy {
			query = query.OrderBy(field)
		}
	}

	// 处理 LIMIT 和 OFFSET
	if schema.Limit > 0 {
		query = query.Limit(schema.Limit, schema.Offset)
	}

	// 生成 SQL
	return query.ToSQL()
}

// GenerateAdvancedSQL 处理高级条件查询
func GenerateAdvancedSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Select   []string                     `json:"select"`
		From     string                       `json:"from"`
		Where    map[string]any               `json:"where"`
		AdvWhere map[string]AdvancedCondition `json:"adv_where"`
		OrderBy  []string                     `json:"order_by"`
		GroupBy  []string                     `json:"group_by"`
		Limit    int                          `json:"limit"`
		Offset   int                          `json:"offset"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	// 构建查询
	query := builder.Dialect(builder.MYSQL).Select(data.Select...).From(data.From)

	// 处理基本WHERE条件
	if len(data.Where) > 0 {
		query = query.Where(builder.Eq(data.Where))
	}

	// 处理高级WHERE条件
	if len(data.AdvWhere) > 0 {
		conds := make([]builder.Cond, 0, len(data.AdvWhere))
		// 排序下key，生成的条件不会随机序
		keys := make([]string, 0, len(data.AdvWhere))
		for k := range data.AdvWhere {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		for _, field := range keys {
			condition := data.AdvWhere[field]
			switch condition.Operator {
			case "=":
				conds = append(conds, builder.Eq{field: condition.Value})
			case ">":
				conds = append(conds, builder.Gt{field: condition.Value})
			case "<":
				conds = append(conds, builder.Lt{field: condition.Value})
			case ">=":
				conds = append(conds, builder.Gte{field: condition.Value})
			case "!=":
				conds = append(conds, builder.Neq{field: condition.Value})
			case "<=":
				conds = append(conds, builder.Lte{field: condition.Value})
			case "IN":
				conds = append(conds, builder.In(field, condition.Value))
			case "LIKE":
				conds = append(conds, builder.Like{0: field, 1: condition.Value.(string)})
			case "IS NULL":
				conds = append(conds, builder.IsNull{field})
			case "IS NOT NULL":
				conds = append(conds, builder.NotNull{field})
			case "BETWEEN":
				if values, ok := condition.Value.([]any); ok && len(values) == 2 {
					conds = append(conds, builder.Between{Col: field, LessVal: values[0], MoreVal: values[1]})
				} else {
					return "", nil, fmt.Errorf("BETWEEN操作符需要两个值的数组")
				}
			case "NOT IN":
				conds = append(conds, builder.NotIn(field, condition.Value))
			case "NOT LIKE":
				// xorm builder没有NotLike，使用Not包装Like
				conds = append(conds, builder.Not{builder.Like{0: field, 1: condition.Value.(string)}})
			default:
				return "", nil, fmt.Errorf("不支持的操作符: %s", condition.Operator)
			}
		}

		if len(conds) > 0 {
			query = query.Where(builder.And(conds...))
		}
	}

	// 处理其他条件
	if len(data.GroupBy) > 0 {
		for _, field := range data.GroupBy {
			query = query.GroupBy(field)
		}
	}

	if len(data.OrderBy) > 0 {
		for _, field := range data.OrderBy {
			query = query.OrderBy(field)
		}
	}

	if data.Limit > 0 {
		query = query.Limit(data.Limit, data.Offset)
	}

	return query.ToSQL()
}

// GenerateInsertSQL 从JSON生成INSERT SQL
func GenerateInsertSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Table  string         `json:"table"`
		Values map[string]any `json:"values"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}
	if len(data.Values) == 0 {
		return "", nil, fmt.Errorf("values不能为空")
	}

	return builder.Dialect(builder.MYSQL).Insert(builder.Eq(data.Values)).Into(data.Table).ToSQL()
}

// GenerateUpdateSQL 从JSON生成UPDATE SQL
func GenerateUpdateSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Table  string         `json:"table"`
		Values map[string]any `json:"values"`
		Where  map[string]any `json:"where"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}
	if len(data.Values) == 0 {
		return "", nil, fmt.Errorf("values不能为空")
	}

	query := builder.Dialect(builder.MYSQL).Update(builder.Eq(data.Values)).From(data.Table)
	if len(data.Where) > 0 {
		query = query.Where(builder.Eq(data.Where))
	}

	return query.ToSQL()
}

// GenerateDeleteSQL 从JSON生成DELETE SQL
func GenerateDeleteSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Table string         `json:"table"`
		Where map[string]any `json:"where"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}

	var query *builder.Builder
	if len(data.Where) > 0 {
		query = builder.Dialect(builder.MYSQL).Delete(builder.Eq(data.Where)).From(data.Table)
	} else {
		query = builder.Dialect(builder.MYSQL).Delete().From(data.Table)
	}

	return query.ToSQL()
}

// GenerateBatchInsertSQL 从JSON生成批量INSERT SQL
func GenerateBatchInsertSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Table   string   `json:"table"`
		Columns []string `json:"columns"`
		Values  [][]any  `json:"values"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	// 验证数据
	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}
	if len(data.Columns) == 0 {
		return "", nil, fmt.Errorf("columns不能为空")
	}
	if len(data.Values) == 0 {
		return "", nil, fmt.Errorf("values不能为空")
	}

	// 检查每行的值数量是否与列数量匹配
	for i, row := range data.Values {
		if len(row) != len(data.Columns) {
			return "", nil, fmt.Errorf("第%d行的值数量(%d)与列数量(%d)不匹配", i+1, len(row), len(data.Columns))
		}
	}

	// xorm builder不直接支持批量插入，我们手动构建SQL
	// 构建INSERT部分
	sql := fmt.Sprintf("INSERT INTO %s (%s)", data.Table, strings.Join(data.Columns, ", "))

	// 构建VALUES部分
	valueStrings := make([]string, 0, len(data.Values))
	valueArgs := make([]any, 0, len(data.Values)*len(data.Columns))

	// 为每行构建占位符并收集参数
	for _, row := range data.Values {
		placeholders := make([]string, len(data.Columns))
		for i := range data.Columns {
			placeholders[i] = "?"
			valueArgs = append(valueArgs, row[i])
		}
		valueStrings = append(valueStrings, fmt.Sprintf("(%s)", strings.Join(placeholders, ", ")))
	}

	// 组合成最终SQL
	sql += " VALUES " + strings.Join(valueStrings, ", ")

	return sql, valueArgs, nil
}

// GenerateComplexSQL 处理复杂嵌套条件查询
func GenerateComplexSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Select       []string          `json:"select"`
		From         string            `json:"from"`
		Where        any               `json:"where"`         // 可以是简单条件或复杂条件
		ComplexWhere *ComplexCondition `json:"complex_where"` // 复杂嵌套条件
		JoinTables   []JoinTable       `json:"join_tables"`
		OrderBy      []string          `json:"order_by"`
		GroupBy      []string          `json:"group_by"`
		Having       any               `json:"having"`
		Limit        int               `json:"limit"`
		Offset       int               `json:"offset"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	// 验证必要字段
	if len(data.Select) == 0 {
		return "", nil, fmt.Errorf("select字段不能为空")
	}
	if data.From == "" {
		return "", nil, fmt.Errorf("from字段不能为空")
	}

	// 构建查询
	query := builder.Dialect(builder.MYSQL).Select(data.Select...).From(data.From)

	// 处理 JOIN
	for _, join := range data.JoinTables {
		joinCond, err := buildConditionFromMap(join.Condition)
		if err != nil {
			return "", nil, fmt.Errorf("构建JOIN条件失败: %w", err)
		}

		switch join.Type {
		case "LEFT":
			query = query.LeftJoin(join.Table, joinCond)
		case "RIGHT":
			query = query.RightJoin(join.Table, joinCond)
		case "INNER":
			query = query.InnerJoin(join.Table, joinCond)
		default:
			return "", nil, fmt.Errorf("不支持的JOIN类型: %s", join.Type)
		}
	}

	// 处理WHERE条件
	if data.Where != nil {
		whereCond, err := buildCondition(data.Where)
		if err != nil {
			return "", nil, fmt.Errorf("构建WHERE条件失败: %w", err)
		}
		query = query.Where(whereCond)
	}

	// 处理复杂WHERE条件
	if data.ComplexWhere != nil {
		complexCond, err := buildComplexCondition(*data.ComplexWhere)
		if err != nil {
			return "", nil, fmt.Errorf("构建复杂WHERE条件失败: %w", err)
		}
		if data.Where != nil {
			// 如果已有WHERE条件，获取现有条件并用AND连接
			existingCond, err := buildCondition(data.Where)
			if err != nil {
				return "", nil, fmt.Errorf("构建WHERE条件失败: %w", err)
			}
			query = query.Where(builder.And(existingCond, complexCond))
		} else {
			query = query.Where(complexCond)
		}
	}

	// 处理其他条件
	if len(data.GroupBy) > 0 {
		for _, field := range data.GroupBy {
			query = query.GroupBy(field)
		}
	}

	if data.Having != nil {
		havingCond, err := buildCondition(data.Having)
		if err != nil {
			return "", nil, fmt.Errorf("构建HAVING条件失败: %w", err)
		}
		query = query.Having(havingCond)
	}

	if len(data.OrderBy) > 0 {
		for _, field := range data.OrderBy {
			query = query.OrderBy(field)
		}
	}

	if data.Limit > 0 {
		query = query.Limit(data.Limit, data.Offset)
	}

	return query.ToSQL()
}

// buildCondition 构建条件，支持简单条件和复杂条件
func buildCondition(condition any) (builder.Cond, error) {
	switch cond := condition.(type) {
	case map[string]any:
		// 简单的键值对条件
		return builder.Eq(cond), nil
	case ComplexCondition:
		return buildComplexCondition(cond)
	default:
		return nil, fmt.Errorf("不支持的条件类型")
	}
}

// buildConditionFromMap 从map构建条件
func buildConditionFromMap(condMap map[string]any) (builder.Cond, error) {
	if len(condMap) == 0 {
		return nil, fmt.Errorf("条件不能为空")
	}
	return builder.Eq(condMap), nil
}

// buildComplexCondition 构建复杂嵌套条件
func buildComplexCondition(complexCond ComplexCondition) (builder.Cond, error) {
	if len(complexCond.Conditions) == 0 {
		return nil, fmt.Errorf("复杂条件不能为空")
	}

	var conds []builder.Cond
	for _, item := range complexCond.Conditions {
		var cond builder.Cond
		var err error

		if item.Nested != nil {
			// 嵌套条件
			cond, err = buildComplexCondition(*item.Nested)
		} else {
			// 简单条件
			cond, err = buildSimpleCondition(item.Field, item.Operator, item.Value)
		}

		if err != nil {
			return nil, err
		}
		conds = append(conds, cond)
	}

	// 根据逻辑操作符组合条件
	switch strings.ToUpper(complexCond.Logic) {
	case "AND":
		return builder.And(conds...), nil
	case "OR":
		return builder.Or(conds...), nil
	default:
		return nil, fmt.Errorf("不支持的逻辑操作符: %s", complexCond.Logic)
	}
}

// buildSimpleCondition 构建简单条件
func buildSimpleCondition(field, operator string, value any) (builder.Cond, error) {
	if field == "" {
		return nil, fmt.Errorf("字段名不能为空")
	}

	switch operator {
	case "=":
		return builder.Eq{field: value}, nil
	case ">":
		return builder.Gt{field: value}, nil
	case "<":
		return builder.Lt{field: value}, nil
	case ">=":
		return builder.Gte{field: value}, nil
	case "<=":
		return builder.Lte{field: value}, nil
	case "!=", "<>":
		return builder.Neq{field: value}, nil
	case "IN":
		return builder.In(field, value), nil
	case "NOT IN":
		return builder.NotIn(field, value), nil
	case "LIKE":
		return builder.Like{0: field, 1: value.(string)}, nil
	case "NOT LIKE":
		return builder.Not{builder.Like{0: field, 1: value.(string)}}, nil
	case "IS NULL":
		return builder.IsNull{field}, nil
	case "IS NOT NULL":
		return builder.NotNull{field}, nil
	case "BETWEEN":
		if values, ok := value.([]any); ok && len(values) == 2 {
			return builder.Between{Col: field, LessVal: values[0], MoreVal: values[1]}, nil
		}
		return nil, fmt.Errorf("BETWEEN操作符需要两个值的数组")
	default:
		return nil, fmt.Errorf("不支持的操作符: %s", operator)
	}
}

// GenerateConditionalSQL 处理条件性查询（IF-THEN-ELSE逻辑）
func GenerateConditionalSQL(jsonStr string) (string, []any, error) {
	var data struct {
		Select           []string          `json:"select"`
		From             string            `json:"from"`
		ConditionalQuery *ConditionalQuery `json:"conditional_query"`
		BaseWhere        any               `json:"base_where,omitempty"`
		JoinTables       []JoinTable       `json:"join_tables,omitempty"`
		OrderBy          []string          `json:"order_by,omitempty"`
		GroupBy          []string          `json:"group_by,omitempty"`
		Limit            int               `json:"limit,omitempty"`
		Offset           int               `json:"offset,omitempty"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	// 验证必要字段
	if len(data.Select) == 0 {
		return "", nil, fmt.Errorf("select字段不能为空")
	}
	if data.From == "" {
		return "", nil, fmt.Errorf("from字段不能为空")
	}

	// 构建基础查询
	query := builder.Dialect(builder.MYSQL).Select(data.Select...).From(data.From)

	// 处理 JOIN
	for _, join := range data.JoinTables {
		joinCond, err := buildConditionFromMap(join.Condition)
		if err != nil {
			return "", nil, fmt.Errorf("构建JOIN条件失败: %w", err)
		}

		switch join.Type {
		case "LEFT":
			query = query.LeftJoin(join.Table, joinCond)
		case "RIGHT":
			query = query.RightJoin(join.Table, joinCond)
		case "INNER":
			query = query.InnerJoin(join.Table, joinCond)
		default:
			return "", nil, fmt.Errorf("不支持的JOIN类型: %s", join.Type)
		}
	}

	// 处理基础WHERE条件
	if data.BaseWhere != nil {
		baseCond, err := buildCondition(data.BaseWhere)
		if err != nil {
			return "", nil, fmt.Errorf("构建基础WHERE条件失败: %w", err)
		}
		query = query.Where(baseCond)
	}

	// 处理条件性查询
	if data.ConditionalQuery != nil {
		conditionalCond, err := buildConditionalCondition(*data.ConditionalQuery)
		if err != nil {
			return "", nil, fmt.Errorf("构建条件性查询失败: %w", err)
		}

		// 直接添加条件性查询条件，不重复处理基础WHERE条件
		query = query.Where(conditionalCond)
	}

	// 处理其他条件
	if len(data.GroupBy) > 0 {
		for _, field := range data.GroupBy {
			query = query.GroupBy(field)
		}
	}

	if len(data.OrderBy) > 0 {
		for _, field := range data.OrderBy {
			query = query.OrderBy(field)
		}
	}

	if data.Limit > 0 {
		query = query.Limit(data.Limit, data.Offset)
	}

	return query.ToSQL()
}

// buildConditionalCondition 构建条件性查询条件
func buildConditionalCondition(condQuery ConditionalQuery) (builder.Cond, error) {
	// 构建IF条件
	ifCond, err := buildConditionItem(condQuery.If)
	if err != nil {
		return nil, fmt.Errorf("构建IF条件失败: %w", err)
	}

	// 构建THEN部分的条件
	thenCond, err := buildQueryPartCondition(condQuery.Then)
	if err != nil {
		return nil, fmt.Errorf("构建THEN条件失败: %w", err)
	}

	// 如果有ELSE部分，构建ELSE条件
	if condQuery.Else != nil {
		elseCond, err := buildQueryPartCondition(*condQuery.Else)
		if err != nil {
			return nil, fmt.Errorf("构建ELSE条件失败: %w", err)
		}

		// 使用CASE WHEN逻辑：IF条件为真时使用THEN，否则使用ELSE
		return builder.Or(
			builder.And(ifCond, thenCond),
			builder.And(builder.Not{ifCond}, elseCond),
		), nil
	}

	// 只有IF-THEN，没有ELSE
	return builder.And(ifCond, thenCond), nil
}

// buildConditionItem 构建条件项
func buildConditionItem(item ConditionItem) (builder.Cond, error) {
	if item.Nested != nil {
		return buildComplexCondition(*item.Nested)
	}
	return buildSimpleCondition(item.Field, item.Operator, item.Value)
}

// buildQueryPartCondition 构建查询部分的条件
func buildQueryPartCondition(part QueryPart) (builder.Cond, error) {
	if part.Where != nil {
		return buildCondition(part.Where)
	}
	// 如果没有WHERE条件，返回一个总是为真的条件
	return builder.Eq{"1": "1"}, nil
}
