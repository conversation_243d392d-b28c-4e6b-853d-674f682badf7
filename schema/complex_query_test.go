package schema

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestGenerateComplexSQL 测试复杂嵌套条件查询
func TestGenerateComplexSQL(t *testing.T) {
	tests := []struct {
		name        string
		jsonStr     string
		expectedSQL string
		hasError    bool
	}{
		{
			name: "基本OR条件",
			jsonStr: `{
				"select": ["id", "name", "age"],
				"from": "users",
				"complex_where": {
					"logic": "OR",
					"conditions": [
						{
							"field": "age",
							"operator": ">",
							"value": 18
						},
						{
							"field": "status",
							"operator": "=",
							"value": "vip"
						}
					]
				}
			}`,
			expectedSQL: "SELECT id,name,age FROM users WHERE age>? OR status=?",
			hasError:    false,
		},
		{
			name: "嵌套AND和OR条件",
			jsonStr: `{
				"select": ["id", "name"],
				"from": "users",
				"complex_where": {
					"logic": "AND",
					"conditions": [
						{
							"field": "status",
							"operator": "=",
							"value": "active"
						},
						{
							"nested": {
								"logic": "OR",
								"conditions": [
									{
										"field": "age",
										"operator": ">",
										"value": 18
									},
									{
										"field": "vip_level",
										"operator": ">=",
										"value": 3
									}
								]
							}
						}
					]
				}
			}`,
			expectedSQL: "SELECT id,name FROM users WHERE status=? AND (age>? OR vip_level>=?)",
			hasError:    false,
		},
		{
			name: "BETWEEN操作符",
			jsonStr: `{
				"select": ["id", "name", "age"],
				"from": "users",
				"complex_where": {
					"logic": "AND",
					"conditions": [
						{
							"field": "age",
							"operator": "BETWEEN",
							"value": [18, 65]
						},
						{
							"field": "status",
							"operator": "=",
							"value": "active"
						}
					]
				}
			}`,
			expectedSQL: "SELECT id,name,age FROM users WHERE age BETWEEN ? AND ? AND status=?",
			hasError:    false,
		},
		{
			name: "NOT IN和NOT LIKE操作符",
			jsonStr: `{
				"select": ["id", "name"],
				"from": "users",
				"complex_where": {
					"logic": "AND",
					"conditions": [
						{
							"field": "status",
							"operator": "NOT IN",
							"value": ["banned", "deleted"]
						},
						{
							"field": "email",
							"operator": "NOT LIKE",
							"value": "%@spam.com"
						}
					]
				}
			}`,
			expectedSQL: "SELECT id,name FROM users WHERE status NOT IN (?,?) AND NOT email LIKE ?",
			hasError:    false,
		},
		{
			name: "IS NULL和IS NOT NULL操作符",
			jsonStr: `{
				"select": ["id", "name"],
				"from": "users",
				"complex_where": {
					"logic": "OR",
					"conditions": [
						{
							"field": "deleted_at",
							"operator": "IS NULL",
							"value": null
						},
						{
							"field": "email",
							"operator": "IS NOT NULL",
							"value": null
						}
					]
				}
			}`,
			expectedSQL: "SELECT id,name FROM users WHERE deleted_at IS NULL OR email IS NOT NULL",
			hasError:    false,
		},
		{
			name: "复杂嵌套查询与JOIN",
			jsonStr: `{
				"select": ["u.id", "u.name", "o.order_no"],
				"from": "users u",
				"join_tables": [
					{
						"type": "LEFT",
						"table": "orders o",
						"condition": {
							"u.id": "o.user_id"
						}
					}
				],
				"complex_where": {
					"logic": "AND",
					"conditions": [
						{
							"field": "u.status",
							"operator": "=",
							"value": "active"
						},
						{
							"nested": {
								"logic": "OR",
								"conditions": [
									{
										"field": "o.amount",
										"operator": ">",
										"value": 100
									},
									{
										"field": "u.vip_level",
										"operator": ">=",
										"value": 2
									}
								]
							}
						}
					]
				}
			}`,
			expectedSQL: "SELECT u.id,u.name,o.order_no FROM users u LEFT JOIN orders o ON u.id=? WHERE u.status=? AND (o.amount>? OR u.vip_level>=?)",
			hasError:    false,
		},
		{
			name: "无效的BETWEEN值",
			jsonStr: `{
				"select": ["id"],
				"from": "users",
				"complex_where": {
					"logic": "AND",
					"conditions": [
						{
							"field": "age",
							"operator": "BETWEEN",
							"value": [18]
						}
					]
				}
			}`,
			expectedSQL: "",
			hasError:    true,
		},
		{
			name: "无效的逻辑操作符",
			jsonStr: `{
				"select": ["id"],
				"from": "users",
				"complex_where": {
					"logic": "XOR",
					"conditions": [
						{
							"field": "age",
							"operator": ">",
							"value": 18
						}
					]
				}
			}`,
			expectedSQL: "",
			hasError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sql, _, err := GenerateComplexSQL(tt.jsonStr)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// 简化测试，只检查SQL语句的基本结构
				sqlNoSpaces := strings.Join(strings.Fields(sql), " ")
				expectedNoSpaces := strings.Join(strings.Fields(tt.expectedSQL), " ")
				assert.Equal(t, expectedNoSpaces, sqlNoSpaces)
			}
		})
	}
}

// TestGenerateConditionalSQL 测试条件性查询
func TestGenerateConditionalSQL(t *testing.T) {
	tests := []struct {
		name        string
		jsonStr     string
		expectedSQL string
		hasError    bool
	}{
		{
			name: "基本IF-THEN条件",
			jsonStr: `{
				"select": ["id", "name", "age"],
				"from": "users",
				"conditional_query": {
					"if": {
						"field": "age",
						"operator": ">=",
						"value": 18
					},
					"then": {
						"where": {
							"status": "adult"
						}
					}
				}
			}`,
			expectedSQL: "SELECT id,name,age FROM users WHERE age>=? AND status=?",
			hasError:    false,
		},
		{
			name: "IF-THEN-ELSE条件",
			jsonStr: `{
				"select": ["id", "name"],
				"from": "users",
				"conditional_query": {
					"if": {
						"field": "vip_level",
						"operator": ">",
						"value": 0
					},
					"then": {
						"where": {
							"discount": 0.1
						}
					},
					"else": {
						"where": {
							"discount": 0
						}
					}
				}
			}`,
			expectedSQL: "SELECT id,name FROM users WHERE (vip_level>? AND discount=?) OR (NOT vip_level>? AND discount=?)",
			hasError:    false,
		},
		{
			name: "带基础WHERE的条件查询",
			jsonStr: `{
				"select": ["id", "name"],
				"from": "users",
				"base_where": {
					"status": "active"
				},
				"conditional_query": {
					"if": {
						"field": "age",
						"operator": ">=",
						"value": 18
					},
					"then": {
						"where": {
							"category": "adult"
						}
					}
				}
			}`,
			expectedSQL: "SELECT id,name FROM users WHERE status=? AND age>=? AND category=?",
			hasError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sql, _, err := GenerateConditionalSQL(tt.jsonStr)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// 简化测试，只检查SQL语句的基本结构
				sqlNoSpaces := strings.Join(strings.Fields(sql), " ")
				expectedNoSpaces := strings.Join(strings.Fields(tt.expectedSQL), " ")
				assert.Equal(t, expectedNoSpaces, sqlNoSpaces)
			}
		})
	}
}

// TestBuildSimpleCondition 测试简单条件构建
func TestBuildSimpleCondition(t *testing.T) {
	tests := []struct {
		name     string
		field    string
		operator string
		value    any
		hasError bool
	}{
		{"等于", "age", "=", 25, false},
		{"大于", "age", ">", 18, false},
		{"小于", "age", "<", 65, false},
		{"大于等于", "age", ">=", 18, false},
		{"小于等于", "age", "<=", 65, false},
		{"不等于", "age", "!=", 0, false},
		{"IN操作", "status", "IN", []string{"active", "pending"}, false},
		{"NOT IN操作", "status", "NOT IN", []string{"banned", "deleted"}, false},
		{"LIKE操作", "name", "LIKE", "%john%", false},
		{"NOT LIKE操作", "email", "NOT LIKE", "%@spam.com", false},
		{"IS NULL操作", "deleted_at", "IS NULL", nil, false},
		{"IS NOT NULL操作", "email", "IS NOT NULL", nil, false},
		{"BETWEEN操作", "age", "BETWEEN", []any{18, 65}, false},
		{"无效BETWEEN值", "age", "BETWEEN", []any{18}, true},
		{"空字段名", "", "=", 25, true},
		{"不支持的操作符", "age", "REGEX", "pattern", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := buildSimpleCondition(tt.field, tt.operator, tt.value)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
